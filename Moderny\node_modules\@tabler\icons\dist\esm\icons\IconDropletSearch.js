/**
 * @tabler/icons v2.47.0 - MIT
 */

var IconDropletSearch = (IconDropletSearch) => `<svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-droplet-search" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
  <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
  <path d="M18.064 10.877l-4.89 -7.26c-.42 -.625 -1.287 -.803 -1.936 -.397a1.376 1.376 0 0 0 -.41 .397l-4.893 7.26c-1.695 2.838 -1.035 6.441 1.567 8.546a7.13 7.13 0 0 0 4.168 1.572" />
  <path d="M18 18m-3 0a3 3 0 1 0 6 0a3 3 0 1 0 -6 0" />
  <path d="M20.2 20.2l1.8 1.8" />
</svg>`;

export { IconDropletSearch as default };
//# sourceMappingURL=IconDropletSearch.js.map
