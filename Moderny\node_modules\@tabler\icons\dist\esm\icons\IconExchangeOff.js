/**
 * @tabler/icons v2.47.0 - MIT
 */

var IconExchangeOff = (IconExchangeOff) => `<svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-exchange-off" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
  <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
  <path d="M5 18m-2 0a2 2 0 1 0 4 0a2 2 0 1 0 -4 0" />
  <path d="M19 6m-2 0a2 2 0 1 0 4 0a2 2 0 1 0 -4 0" />
  <path d="M19 8v5c0 .594 -.104 1.164 -.294 1.692m-1.692 2.298a4.978 4.978 0 0 1 -3.014 1.01h-3l3 -3" />
  <path d="M14 21l-3 -3" />
  <path d="M5 16v-5c0 -1.632 .782 -3.082 1.992 -4m3.008 -1h3l-3 -3" />
  <path d="M11.501 7.499l1.499 -1.499" />
  <path d="M3 3l18 18" />
</svg>`;

export { IconExchangeOff as default };
//# sourceMappingURL=IconExchangeOff.js.map
