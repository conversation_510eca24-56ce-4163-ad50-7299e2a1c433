/**
 * @tabler/icons v2.47.0 - MIT
 */

var IconEaseOutControlPoint = (IconEaseOutControlPoint) => `<svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-ease-out-control-point" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
  <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
  <path d="M3 21s10 -16 18 -16" />
  <path d="M7 5a2 2 0 1 1 -4 0a2 2 0 0 1 4 0z" />
  <path d="M7 5h2" />
  <path d="M14 5h-2" />
</svg>`;

export { IconEaseOutControlPoint as default };
//# sourceMappingURL=IconEaseOutControlPoint.js.map
