/**
 * @tabler/icons-react v2.47.0 - MIT
 */

import createReactComponent from '../createReactComponent.js';

var IconExposure = createReactComponent("exposure", "IconExposure", [
  [
    "path",
    {
      d: "M4 4m0 2a2 2 0 0 1 2 -2h12a2 2 0 0 1 2 2v12a2 2 0 0 1 -2 2h-12a2 2 0 0 1 -2 -2z",
      key: "svg-0"
    }
  ],
  ["path", { d: "M4.6 19.4l14.8 -14.8", key: "svg-1" }],
  ["path", { d: "M7 9h4m-2 -2v4", key: "svg-2" }],
  ["path", { d: "M13 16l4 0", key: "svg-3" }]
]);

export { IconExposure as default };
//# sourceMappingURL=IconExposure.js.map
