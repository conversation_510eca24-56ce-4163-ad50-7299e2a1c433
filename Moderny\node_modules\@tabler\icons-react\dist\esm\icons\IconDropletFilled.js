/**
 * @tabler/icons-react v2.47.0 - MIT
 */

import createReactComponent from '../createReactComponent.js';

var IconDropletFilled = createReactComponent("droplet-filled", "IconDropletFilled", [
  [
    "path",
    {
      d: "M10.708 2.372a2.382 2.382 0 0 0 -.71 .686l-4.892 7.26c-1.981 3.314 -1.22 7.466 1.767 9.882c2.969 2.402 7.286 2.402 10.254 0c2.987 -2.416 3.748 -6.569 1.795 -9.836l-4.919 -7.306c-.722 -1.075 -2.192 -1.376 -3.295 -.686z",
      fill: "currentColor",
      key: "svg-0",
      strokeWidth: "0"
    }
  ]
]);

export { IconDropletFilled as default };
//# sourceMappingURL=IconDropletFilled.js.map
