/**
 * @tabler/icons v2.47.0 - MIT
 */

var IconDropCircle = (IconDropCircle) => `<svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-drop-circle" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
  <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
  <path d="M10.07 15.34c1.115 .88 2.74 .88 3.855 0c1.115 -.88 1.398 -2.388 .671 -3.575l-2.596 -3.765l-2.602 3.765c-.726 1.187 -.443 2.694 .672 3.575z" />
  <path d="M12 12m-9 0a9 9 0 1 0 18 0a9 9 0 1 0 -18 0" />
</svg>`;

export { IconDropCircle as default };
//# sourceMappingURL=IconDropCircle.js.map
