/**
 * @tabler/icons v2.47.0 - MIT
 */

var IconFavicon = (IconFavicon) => `<svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-favicon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
  <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
  <path d="M2 5m0 3a3 3 0 0 1 3 -3h14a3 3 0 0 1 3 3v8a3 3 0 0 1 -3 3h-14a3 3 0 0 1 -3 -3z" />
  <path d="M6 10v4" />
  <path d="M11 10a2 2 0 1 0 0 4" />
  <path d="M16 12m-2 0a2 2 0 1 0 4 0a2 2 0 1 0 -4 0" />
</svg>`;

export { IconFavicon as default };
//# sourceMappingURL=IconFavicon.js.map
