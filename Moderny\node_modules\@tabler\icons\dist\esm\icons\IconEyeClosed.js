/**
 * @tabler/icons v2.47.0 - MIT
 */

var IconEyeClosed = (IconEyeClosed) => `<svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-eye-closed" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
  <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
  <path d="M21 9c-2.4 2.667 -5.4 4 -9 4c-3.6 0 -6.6 -1.333 -9 -4" />
  <path d="M3 15l2.5 -3.8" />
  <path d="M21 14.976l-2.492 -3.776" />
  <path d="M9 17l.5 -4" />
  <path d="M15 17l-.5 -4" />
</svg>`;

export { IconEyeClosed as default };
//# sourceMappingURL=IconEyeClosed.js.map
