/**
 * @tabler/icons v2.47.0 - MIT
 */

var IconDeviceWatchOff = (IconDeviceWatchOff) => `<svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-device-watch-off" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
  <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
  <path d="M10 6h5a3 3 0 0 1 3 3v5m-.89 3.132a2.99 2.99 0 0 1 -2.11 .868h-6a3 3 0 0 1 -3 -3v-6c0 -.817 .327 -1.559 .857 -2.1" />
  <path d="M9 18v3h6v-3" />
  <path d="M9 5v-2h6v3" />
  <path d="M3 3l18 18" />
</svg>`;

export { IconDeviceWatchOff as default };
//# sourceMappingURL=IconDeviceWatchOff.js.map
