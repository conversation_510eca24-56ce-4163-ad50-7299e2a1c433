/**
 * @tabler/icons v2.47.0 - MIT
 */

var IconExplicit = (IconExplicit) => `<svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-explicit" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
  <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
  <path d="M4 4m0 1a1 1 0 0 1 1 -1h14a1 1 0 0 1 1 1v14a1 1 0 0 1 -1 1h-14a1 1 0 0 1 -1 -1z" />
  <path d="M14 8h-4v8h4" />
  <path d="M14 12h-4" />
</svg>`;

export { IconExplicit as default };
//# sourceMappingURL=IconExplicit.js.map
