/**
 * @tabler/icons-react v2.47.0 - MIT
 */

import createReactComponent from '../createReactComponent.js';

var IconDivide = createReactComponent("divide", "IconDivide", [
  ["circle", { cx: "12", cy: "6", r: "1", fill: "currentColor", key: "svg-0" }],
  [
    "circle",
    { cx: "12", cy: "18", r: "1", fill: "currentColor", key: "svg-1" }
  ],
  ["path", { d: "M5 12l14 0", key: "svg-2" }]
]);

export { IconDivide as default };
//# sourceMappingURL=IconDivide.js.map
