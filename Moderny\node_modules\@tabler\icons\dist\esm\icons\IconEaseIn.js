/**
 * @tabler/icons v2.47.0 - MIT
 */

var IconEaseIn = (IconEaseIn) => `<svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-ease-in" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
  <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
  <path d="M3 20c8 0 18 -16 18 -16" />
</svg>`;

export { IconEaseIn as default };
//# sourceMappingURL=IconEaseIn.js.map
