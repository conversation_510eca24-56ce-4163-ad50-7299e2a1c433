/**
 * @tabler/icons-react v2.47.0 - MIT
 */

import createReactComponent from '../createReactComponent.js';

var IconDirectionHorizontal = createReactComponent(
  "direction-horizontal",
  "IconDirectionHorizontal",
  [
    ["path", { d: "M10 9l-3 3l3 3", key: "svg-0" }],
    ["path", { d: "M14 9l3 3l-3 3", key: "svg-1" }]
  ]
);

export { IconDirectionHorizontal as default };
//# sourceMappingURL=IconDirectionHorizontal.js.map
