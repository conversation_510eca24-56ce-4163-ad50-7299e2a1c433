/**
 * @tabler/icons v2.47.0 - MIT
 */

var IconDropletQuestion = (IconDropletQuestion) => `<svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-droplet-question" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
  <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
  <path d="M18.064 10.877l-4.89 -7.26c-.42 -.625 -1.287 -.803 -1.936 -.397a1.376 1.376 0 0 0 -.41 .397l-4.893 7.26c-1.695 2.838 -1.035 6.441 1.567 8.546c2.203 1.782 5.259 2.056 7.723 .82" />
  <path d="M19 22v.01" />
  <path d="M19 19a2.003 2.003 0 0 0 .914 -3.782a1.98 1.98 0 0 0 -2.414 .483" />
</svg>`;

export { IconDropletQuestion as default };
//# sourceMappingURL=IconDropletQuestion.js.map
