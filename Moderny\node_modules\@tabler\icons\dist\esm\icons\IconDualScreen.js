/**
 * @tabler/icons v2.47.0 - MIT
 */

var IconDualScreen = (IconDualScreen) => `<svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-dual-screen" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
  <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
  <path d="M5 4l8 3v15l-8 -3z" />
  <path d="M13 19h6v-15h-14" />
</svg>`;

export { IconDualScreen as default };
//# sourceMappingURL=IconDualScreen.js.map
