/**
 * @tabler/icons v2.47.0 - MIT
 */

var IconElevator = (IconElevator) => `<svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-elevator" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
  <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
  <path d="M5 4m0 1a1 1 0 0 1 1 -1h12a1 1 0 0 1 1 1v14a1 1 0 0 1 -1 1h-12a1 1 0 0 1 -1 -1z" />
  <path d="M10 10l2 -2l2 2" />
  <path d="M10 14l2 2l2 -2" />
</svg>`;

export { IconElevator as default };
//# sourceMappingURL=IconElevator.js.map
