/**
 * @tabler/icons-react v2.47.0 - MIT
 */

import createReactComponent from '../createReactComponent.js';

var IconDialpadFilled = createReactComponent("dialpad-filled", "IconDialpadFilled", [
  [
    "path",
    {
      d: "M6 2h-2a2 2 0 0 0 -2 2v2a2 2 0 0 0 2 2h2a2 2 0 0 0 2 -2v-2a2 2 0 0 0 -2 -2z",
      fill: "currentColor",
      key: "svg-0",
      strokeWidth: "0"
    }
  ],
  [
    "path",
    {
      d: "M20 2h-2a2 2 0 0 0 -2 2v2a2 2 0 0 0 2 2h2a2 2 0 0 0 2 -2v-2a2 2 0 0 0 -2 -2z",
      fill: "currentColor",
      key: "svg-1",
      strokeWidth: "0"
    }
  ],
  [
    "path",
    {
      d: "M13 2h-2a2 2 0 0 0 -2 2v2a2 2 0 0 0 2 2h2a2 2 0 0 0 2 -2v-2a2 2 0 0 0 -2 -2z",
      fill: "currentColor",
      key: "svg-2",
      strokeWidth: "0"
    }
  ],
  [
    "path",
    {
      d: "M6 9h-2a2 2 0 0 0 -2 2v2a2 2 0 0 0 2 2h2a2 2 0 0 0 2 -2v-2a2 2 0 0 0 -2 -2z",
      fill: "currentColor",
      key: "svg-3",
      strokeWidth: "0"
    }
  ],
  [
    "path",
    {
      d: "M20 9h-2a2 2 0 0 0 -2 2v2a2 2 0 0 0 2 2h2a2 2 0 0 0 2 -2v-2a2 2 0 0 0 -2 -2z",
      fill: "currentColor",
      key: "svg-4",
      strokeWidth: "0"
    }
  ],
  [
    "path",
    {
      d: "M13 9h-2a2 2 0 0 0 -2 2v2a2 2 0 0 0 2 2h2a2 2 0 0 0 2 -2v-2a2 2 0 0 0 -2 -2z",
      fill: "currentColor",
      key: "svg-5",
      strokeWidth: "0"
    }
  ],
  [
    "path",
    {
      d: "M13 16h-2a2 2 0 0 0 -2 2v2a2 2 0 0 0 2 2h2a2 2 0 0 0 2 -2v-2a2 2 0 0 0 -2 -2z",
      fill: "currentColor",
      key: "svg-6",
      strokeWidth: "0"
    }
  ]
]);

export { IconDialpadFilled as default };
//# sourceMappingURL=IconDialpadFilled.js.map
