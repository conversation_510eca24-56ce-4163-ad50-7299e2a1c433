/**
 * @tabler/icons v2.47.0 - MIT
 */

var IconDivide = (IconDivide) => `<svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-divide" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
  <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
  <circle cx="12" cy="6" r="1" fill="currentColor" />
  <circle cx="12" cy="18" r="1" fill="currentColor" />
  <path d="M5 12l14 0" />
</svg>`;

export { IconDivide as default };
//# sourceMappingURL=IconDivide.js.map
