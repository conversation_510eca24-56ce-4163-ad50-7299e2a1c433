/**
 * @tabler/icons v2.47.0 - MIT
 */

var IconEPassport = (IconEPassport) => `<svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-e-passport" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
  <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
  <path d="M2 5m0 2a2 2 0 0 1 2 -2h16a2 2 0 0 1 2 2v10a2 2 0 0 1 -2 2h-16a2 2 0 0 1 -2 -2z" />
  <path d="M12 12m-3 0a3 3 0 1 0 6 0a3 3 0 1 0 -6 0" />
  <path d="M9 12h-7" />
  <path d="M15 12h7" />
</svg>`;

export { IconEPassport as default };
//# sourceMappingURL=IconEPassport.js.map
