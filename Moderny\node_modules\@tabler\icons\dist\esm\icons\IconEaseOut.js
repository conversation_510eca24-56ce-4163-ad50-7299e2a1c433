/**
 * @tabler/icons v2.47.0 - MIT
 */

var IconEaseOut = (IconEaseOut) => `<svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-ease-out" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
  <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
  <path d="M3 20s10 -16 18 -16" />
</svg>`;

export { IconEaseOut as default };
//# sourceMappingURL=IconEaseOut.js.map
