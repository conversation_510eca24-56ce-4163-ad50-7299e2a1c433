/**
 * @tabler/icons-react v2.47.0 - MIT
 */

import createReactComponent from '../createReactComponent.js';

var IconEyeShare = createReactComponent("eye-share", "IconEyeShare", [
  ["path", { d: "M10 12a2 2 0 1 0 4 0a2 2 0 0 0 -4 0", key: "svg-0" }],
  [
    "path",
    {
      d: "M12.597 17.981a9.467 9.467 0 0 1 -.597 .019c-3.6 0 -6.6 -2 -9 -6c2.4 -4 5.4 -6 9 -6c3.6 0 6.6 2 9 6c-.205 .342 -.415 .67 -.63 .983",
      key: "svg-1"
    }
  ],
  ["path", { d: "M16 22l5 -5", key: "svg-2" }],
  ["path", { d: "M21 21.5v-4.5h-4.5", key: "svg-3" }]
]);

export { IconEyeShare as default };
//# sourceMappingURL=IconEyeShare.js.map
