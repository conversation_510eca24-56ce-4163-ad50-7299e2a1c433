/**
 * @tabler/icons-react v2.47.0 - MIT
 */

import createReactComponent from '../createReactComponent.js';

var IconDotsCircleHorizontal = createReactComponent(
  "dots-circle-horizontal",
  "IconDotsCircleHorizontal",
  [
    ["path", { d: "M12 12m-9 0a9 9 0 1 0 18 0a9 9 0 1 0 -18 0", key: "svg-0" }],
    ["path", { d: "M8 12l0 .01", key: "svg-1" }],
    ["path", { d: "M12 12l0 .01", key: "svg-2" }],
    ["path", { d: "M16 12l0 .01", key: "svg-3" }]
  ]
);

export { IconDotsCircleHorizontal as default };
//# sourceMappingURL=IconDotsCircleHorizontal.js.map
