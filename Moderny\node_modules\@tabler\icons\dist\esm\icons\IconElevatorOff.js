/**
 * @tabler/icons v2.47.0 - MIT
 */

var IconElevatorOff = (IconElevatorOff) => `<svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-elevator-off" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
  <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
  <path d="M8 4h10a1 1 0 0 1 1 1v10m0 4a1 1 0 0 1 -1 1h-12a1 1 0 0 1 -1 -1v-14" />
  <path d="M12 8l2 2" />
  <path d="M10 14l2 2l2 -2" />
  <path d="M3 3l18 18" />
</svg>`;

export { IconElevatorOff as default };
//# sourceMappingURL=IconElevatorOff.js.map
