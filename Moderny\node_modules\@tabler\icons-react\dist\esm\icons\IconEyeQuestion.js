/**
 * @tabler/icons-react v2.47.0 - MIT
 */

import createReactComponent from '../createReactComponent.js';

var IconEyeQuestion = createReactComponent("eye-question", "IconEyeQuestion", [
  ["path", { d: "M10 12a2 2 0 1 0 4 0a2 2 0 0 0 -4 0", key: "svg-0" }],
  [
    "path",
    {
      d: "M14.071 17.764a8.989 8.989 0 0 1 -2.071 .236c-3.6 0 -6.6 -2 -9 -6c2.4 -4 5.4 -6 9 -6c3.346 0 6.173 1.727 8.482 5.182",
      key: "svg-1"
    }
  ],
  ["path", { d: "M19 22v.01", key: "svg-2" }],
  [
    "path",
    {
      d: "M19 19a2.003 2.003 0 0 0 .914 -3.782a1.98 1.98 0 0 0 -2.414 .483",
      key: "svg-3"
    }
  ]
]);

export { IconEyeQuestion as default };
//# sourceMappingURL=IconEyeQuestion.js.map
