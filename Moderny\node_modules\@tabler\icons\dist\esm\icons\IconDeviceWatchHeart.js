/**
 * @tabler/icons v2.47.0 - MIT
 */

var IconDeviceWatchHeart = (IconDeviceWatchHeart) => `<svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-device-watch-heart" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
  <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
  <path d="M10 18h-1a3 3 0 0 1 -3 -3v-6a3 3 0 0 1 3 -3h6a3 3 0 0 1 3 3v2" />
  <path d="M9 18v3h2.5" />
  <path d="M9 6v-3h6v3" />
  <path d="M18 22l3.35 -3.284a2.143 2.143 0 0 0 .005 -3.071a2.242 2.242 0 0 0 -3.129 -.006l-.224 .22l-.223 -.22a2.242 2.242 0 0 0 -3.128 -.006a2.143 2.143 0 0 0 -.006 3.071l3.355 3.296z" />
</svg>`;

export { IconDeviceWatchHeart as default };
//# sourceMappingURL=IconDeviceWatchHeart.js.map
