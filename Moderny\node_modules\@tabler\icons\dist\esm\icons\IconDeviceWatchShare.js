/**
 * @tabler/icons v2.47.0 - MIT
 */

var IconDeviceWatchShare = (IconDeviceWatchShare) => `<svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-device-watch-share" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
  <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
  <path d="M12.5 18h-3.5a3 3 0 0 1 -3 -3v-6a3 3 0 0 1 3 -3h6a3 3 0 0 1 3 3v4" />
  <path d="M9 18v3h3" />
  <path d="M9 6v-3h6v3" />
  <path d="M16 22l5 -5" />
  <path d="M21 21.5v-4.5h-4.5" />
</svg>`;

export { IconDeviceWatchShare as default };
//# sourceMappingURL=IconDeviceWatchShare.js.map
