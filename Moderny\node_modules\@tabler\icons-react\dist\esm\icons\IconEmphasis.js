/**
 * @tabler/icons-react v2.47.0 - MIT
 */

import createReactComponent from '../createReactComponent.js';

var IconEmphasis = createReactComponent("emphasis", "IconEmphasis", [
  ["path", { d: "M16 5h-8v10h8m-1 -5h-7", key: "svg-0" }],
  ["path", { d: "M6 20l0 .01", key: "svg-1" }],
  ["path", { d: "M10 20l0 .01", key: "svg-2" }],
  ["path", { d: "M14 20l0 .01", key: "svg-3" }],
  ["path", { d: "M18 20l0 .01", key: "svg-4" }]
]);

export { IconEmphasis as default };
//# sourceMappingURL=IconEmphasis.js.map
