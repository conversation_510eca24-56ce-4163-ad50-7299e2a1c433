/**
 * @tabler/icons-react v2.47.0 - MIT
 */

import createReactComponent from '../createReactComponent.js';

var IconFileFilled = createReactComponent("file-filled", "IconFileFilled", [
  [
    "path",
    {
      d: "M12 2l.117 .007a1 1 0 0 1 .876 .876l.007 .117v4l.005 .15a2 2 0 0 0 1.838 1.844l.157 .006h4l.117 .007a1 1 0 0 1 .876 .876l.007 .117v9a3 3 0 0 1 -2.824 2.995l-.176 .005h-10a3 3 0 0 1 -2.995 -2.824l-.005 -.176v-14a3 3 0 0 1 2.824 -2.995l.176 -.005h5z",
      fill: "currentColor",
      key: "svg-0",
      strokeWidth: "0"
    }
  ],
  [
    "path",
    {
      d: "M19 7h-4l-.001 -4.001z",
      fill: "currentColor",
      key: "svg-1",
      strokeWidth: "0"
    }
  ]
]);

export { IconFileFilled as default };
//# sourceMappingURL=IconFileFilled.js.map
