/**
 * @tabler/icons v2.47.0 - MIT
 */

var IconDeviceWatchQuestion = (IconDeviceWatchQuestion) => `<svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-device-watch-question" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
  <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
  <path d="M14 18h-5a3 3 0 0 1 -3 -3v-6a3 3 0 0 1 3 -3h6a3 3 0 0 1 3 3v2" />
  <path d="M9 18v3h6v-2" />
  <path d="M9 6v-3h6v3" />
  <path d="M19 22v.01" />
  <path d="M19 19a2.003 2.003 0 0 0 .914 -3.782a1.98 1.98 0 0 0 -2.414 .483" />
</svg>`;

export { IconDeviceWatchQuestion as default };
//# sourceMappingURL=IconDeviceWatchQuestion.js.map
