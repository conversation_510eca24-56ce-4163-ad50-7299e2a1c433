/**
 * @tabler/icons v2.47.0 - MIT
 */

var IconDirectionArrows = (IconDirectionArrows) => `<svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-direction-arrows" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
  <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
  <path d="M3 12a9 9 0 1 0 18 0a9 9 0 0 0 -18 0" />
  <path d="M8 11l-1 1l1 1" />
  <path d="M11 8l1 -1l1 1" />
  <path d="M16 11l1 1l-1 1" />
  <path d="M11 16l1 1l1 -1" />
</svg>`;

export { IconDirectionArrows as default };
//# sourceMappingURL=IconDirectionArrows.js.map
