/**
 * @tabler/icons-react v2.47.0 - MIT
 */

import createReactComponent from '../createReactComponent.js';

var IconEyeglass = createReactComponent("eyeglass", "IconEyeglass", [
  ["path", { d: "M8 4h-2l-3 10", key: "svg-0" }],
  ["path", { d: "M16 4h2l3 10", key: "svg-1" }],
  ["path", { d: "M10 16l4 0", key: "svg-2" }],
  ["path", { d: "M21 16.5a3.5 3.5 0 0 1 -7 0v-2.5h7v2.5", key: "svg-3" }],
  ["path", { d: "M10 16.5a3.5 3.5 0 0 1 -7 0v-2.5h7v2.5", key: "svg-4" }]
]);

export { IconEyeglass as default };
//# sourceMappingURL=IconEyeglass.js.map
