/**
 * @tabler/icons v2.47.0 - MIT
 */

var IconEggFried = (IconEggFried) => `<svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-egg-fried" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
  <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
  <path d="M12 12m-3 0a3 3 0 1 0 6 0a3 3 0 1 0 -6 0" />
  <path d="M14 3a5 5 0 0 1 4.872 6.13a3 3 0 0 1 .178 5.681a3 3 0 1 1 -4.684 3.626a5 5 0 1 1 -8.662 -5a5 5 0 1 1 4.645 -8.856a4.982 4.982 0 0 1 3.651 -1.585z" />
</svg>`;

export { IconEggFried as default };
//# sourceMappingURL=IconEggFried.js.map
