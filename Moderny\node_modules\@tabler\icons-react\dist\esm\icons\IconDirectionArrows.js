/**
 * @tabler/icons-react v2.47.0 - MIT
 */

import createReactComponent from '../createReactComponent.js';

var IconDirectionArrows = createReactComponent("direction-arrows", "IconDirectionArrows", [
  ["path", { d: "M3 12a9 9 0 1 0 18 0a9 9 0 0 0 -18 0", key: "svg-0" }],
  ["path", { d: "M8 11l-1 1l1 1", key: "svg-1" }],
  ["path", { d: "M11 8l1 -1l1 1", key: "svg-2" }],
  ["path", { d: "M16 11l1 1l-1 1", key: "svg-3" }],
  ["path", { d: "M11 16l1 1l1 -1", key: "svg-4" }]
]);

export { IconDirectionArrows as default };
//# sourceMappingURL=IconDirectionArrows.js.map
